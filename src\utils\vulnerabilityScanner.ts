// Client-side vulnerability scanning and security monitoring

interface VulnerabilityReport {
  id: string;
  type: 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO';
  category: string;
  description: string;
  recommendation: string;
  timestamp: number;
  fixed: boolean;
}

interface SecurityScanResult {
  score: number; // 0-100
  vulnerabilities: VulnerabilityReport[];
  recommendations: string[];
  lastScan: number;
}

export class VulnerabilityScanner {
  private static instance: VulnerabilityScanner;
  private vulnerabilities: VulnerabilityReport[] = [];

  public static getInstance(): VulnerabilityScanner {
    if (!VulnerabilityScanner.instance) {
      VulnerabilityScanner.instance = new VulnerabilityScanner();
    }
    return VulnerabilityScanner.instance;
  }

  // Comprehensive security scan
  async performSecurityScan(): Promise<SecurityScanResult> {
    console.log('🔍 Starting security scan...');
    
    this.vulnerabilities = [];
    
    // Run all security checks
    await Promise.all([
      this.checkHTTPS(),
      this.checkSecurityHeaders(),
      this.checkCookieSecurity(),
      this.checkFormSecurity(),
      this.checkThirdPartyScripts(),
      this.checkLocalStorageSecurity(),
      this.checkPasswordFields(),
      this.checkMixedContent(),
      this.checkCSPCompliance(),
      this.checkSensitiveDataExposure()
    ]);

    const score = this.calculateSecurityScore();
    const recommendations = this.generateRecommendations();

    const result: SecurityScanResult = {
      score,
      vulnerabilities: [...this.vulnerabilities],
      recommendations,
      lastScan: Date.now()
    };

    // Store scan results
    localStorage.setItem('security-scan-result', JSON.stringify(result));
    
    console.log(`🔍 Security scan completed. Score: ${score}/100`);
    return result;
  }

  private async checkHTTPS(): Promise<void> {
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      this.addVulnerability({
        type: 'HIGH',
        category: 'Transport Security',
        description: 'Website is not using HTTPS',
        recommendation: 'Enable HTTPS with a valid SSL certificate'
      });
    }
  }

  private async checkSecurityHeaders(): Promise<void> {
    try {
      const response = await fetch(window.location.href, { method: 'HEAD' });
      const headers = response.headers;

      // Check for security headers
      const requiredHeaders = [
        'x-frame-options',
        'x-content-type-options',
        'x-xss-protection',
        'strict-transport-security',
        'content-security-policy'
      ];

      requiredHeaders.forEach(header => {
        if (!headers.get(header)) {
          this.addVulnerability({
            type: 'MEDIUM',
            category: 'Security Headers',
            description: `Missing ${header} header`,
            recommendation: `Add ${header} header to prevent security vulnerabilities`
          });
        }
      });
    } catch (error) {
      console.warn('Could not check security headers:', error);
    }
  }

  private async checkCookieSecurity(): Promise<void> {
    const cookies = document.cookie.split(';');
    
    cookies.forEach(cookie => {
      const [name] = cookie.trim().split('=');
      if (name) {
        // Check if sensitive cookies are secure
        if (name.toLowerCase().includes('session') || name.toLowerCase().includes('auth')) {
          if (!cookie.includes('Secure') || !cookie.includes('HttpOnly')) {
            this.addVulnerability({
              type: 'HIGH',
              category: 'Cookie Security',
              description: `Sensitive cookie "${name}" lacks security flags`,
              recommendation: 'Add Secure and HttpOnly flags to sensitive cookies'
            });
          }
        }
      }
    });
  }

  private async checkFormSecurity(): Promise<void> {
    const forms = document.querySelectorAll('form');
    
    forms.forEach((form, index) => {
      // Check for CSRF protection
      const csrfToken = form.querySelector('input[name*="csrf"], input[name*="token"]');
      if (!csrfToken && form.method.toLowerCase() === 'post') {
        this.addVulnerability({
          type: 'MEDIUM',
          category: 'Form Security',
          description: `Form ${index + 1} lacks CSRF protection`,
          recommendation: 'Add CSRF tokens to all POST forms'
        });
      }

      // Check for autocomplete on sensitive fields
      const sensitiveFields = form.querySelectorAll('input[type="password"], input[name*="credit"], input[name*="ssn"]');
      sensitiveFields.forEach(field => {
        const input = field as HTMLInputElement;
        if (input.autocomplete !== 'off' && input.autocomplete !== 'new-password') {
          this.addVulnerability({
            type: 'LOW',
            category: 'Form Security',
            description: 'Sensitive field allows autocomplete',
            recommendation: 'Disable autocomplete on sensitive form fields'
          });
        }
      });
    });
  }

  private async checkThirdPartyScripts(): Promise<void> {
    const scripts = document.querySelectorAll('script[src]');
    const suspiciousDomains = ['eval', 'document.write', 'innerHTML'];
    
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src;
      
      // Check for external scripts without integrity
      if (src && !src.startsWith(window.location.origin)) {
        if (!script.hasAttribute('integrity')) {
          this.addVulnerability({
            type: 'MEDIUM',
            category: 'Third-party Security',
            description: `External script without integrity check: ${src}`,
            recommendation: 'Add integrity attribute to external scripts'
          });
        }
      }
    });

    // Check for dangerous script content
    const inlineScripts = document.querySelectorAll('script:not([src])');
    inlineScripts.forEach(script => {
      const content = script.textContent || '';
      suspiciousDomains.forEach(dangerous => {
        if (content.includes(dangerous)) {
          this.addVulnerability({
            type: 'HIGH',
            category: 'Script Security',
            description: `Potentially dangerous script content detected: ${dangerous}`,
            recommendation: 'Review and sanitize script content'
          });
        }
      });
    });
  }

  private async checkLocalStorageSecurity(): Promise<void> {
    // Check for sensitive data in localStorage
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth'];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const lowerKey = key.toLowerCase();
        if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
          this.addVulnerability({
            type: 'HIGH',
            category: 'Data Storage',
            description: `Potentially sensitive data in localStorage: ${key}`,
            recommendation: 'Avoid storing sensitive data in localStorage'
          });
        }
      }
    }
  }

  private async checkPasswordFields(): Promise<void> {
    const passwordFields = document.querySelectorAll('input[type="password"]');
    
    passwordFields.forEach(field => {
      const input = field as HTMLInputElement;
      
      // Check for password strength requirements
      if (!input.pattern && !input.minLength) {
        this.addVulnerability({
          type: 'MEDIUM',
          category: 'Password Security',
          description: 'Password field lacks strength requirements',
          recommendation: 'Add password strength validation'
        });
      }
    });
  }

  private async checkMixedContent(): Promise<void> {
    if (window.location.protocol === 'https:') {
      const httpResources = document.querySelectorAll('[src^="http:"], [href^="http:"]');
      
      if (httpResources.length > 0) {
        this.addVulnerability({
          type: 'MEDIUM',
          category: 'Mixed Content',
          description: `${httpResources.length} HTTP resources on HTTPS page`,
          recommendation: 'Update all resources to use HTTPS'
        });
      }
    }
  }

  private async checkCSPCompliance(): Promise<void> {
    // Check if CSP is properly configured
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    const hasCSP = metaCSP || document.querySelector('meta[name="Content-Security-Policy"]');
    
    if (!hasCSP) {
      this.addVulnerability({
        type: 'HIGH',
        category: 'Content Security Policy',
        description: 'No Content Security Policy detected',
        recommendation: 'Implement a strict Content Security Policy'
      });
    }
  }

  private async checkSensitiveDataExposure(): Promise<void> {
    // Check for exposed sensitive data in DOM
    const textContent = document.body.textContent || '';
    const sensitivePatterns = [
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card
      /\b\d{3}-\d{2}-\d{4}\b/, // SSN
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email (in comments)
    ];

    sensitivePatterns.forEach((pattern, index) => {
      if (pattern.test(textContent)) {
        this.addVulnerability({
          type: 'HIGH',
          category: 'Data Exposure',
          description: 'Potentially sensitive data exposed in DOM',
          recommendation: 'Remove or mask sensitive data from client-side code'
        });
      }
    });
  }

  private addVulnerability(vuln: Omit<VulnerabilityReport, 'id' | 'timestamp' | 'fixed'>): void {
    this.vulnerabilities.push({
      id: `vuln-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      fixed: false,
      ...vuln
    });
  }

  private calculateSecurityScore(): number {
    if (this.vulnerabilities.length === 0) return 100;

    const weights = { HIGH: 20, MEDIUM: 10, LOW: 5, INFO: 1 };
    const totalDeduction = this.vulnerabilities.reduce((sum, vuln) => {
      return sum + weights[vuln.type];
    }, 0);

    return Math.max(0, 100 - totalDeduction);
  }

  private generateRecommendations(): string[] {
    const recommendations = new Set<string>();
    
    this.vulnerabilities.forEach(vuln => {
      recommendations.add(vuln.recommendation);
    });

    // Add general recommendations
    recommendations.add('Regularly update dependencies and frameworks');
    recommendations.add('Implement proper input validation and sanitization');
    recommendations.add('Use HTTPS for all communications');
    recommendations.add('Implement proper authentication and authorization');
    recommendations.add('Regular security audits and penetration testing');

    return Array.from(recommendations);
  }

  // Get scan results
  getLastScanResult(): SecurityScanResult | null {
    const result = localStorage.getItem('security-scan-result');
    return result ? JSON.parse(result) : null;
  }

  // Mark vulnerability as fixed
  markVulnerabilityFixed(vulnerabilityId: string): void {
    const result = this.getLastScanResult();
    if (result) {
      const vuln = result.vulnerabilities.find(v => v.id === vulnerabilityId);
      if (vuln) {
        vuln.fixed = true;
        localStorage.setItem('security-scan-result', JSON.stringify(result));
      }
    }
  }

  // Schedule regular scans
  scheduleRegularScans(): void {
    // Scan on page load
    setTimeout(() => {
      this.performSecurityScan().catch(console.error);
    }, 5000);

    // Scan every 24 hours
    setInterval(() => {
      this.performSecurityScan().catch(console.error);
    }, 24 * 60 * 60 * 1000);
  }
}

export const vulnerabilityScanner = VulnerabilityScanner.getInstance();

export default { VulnerabilityScanner, vulnerabilityScanner };

{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "redirects": [{"source": "/index.html", "destination": "/", "permanent": true}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}]}, {"source": "/sitemap.xsl", "headers": [{"key": "Content-Type", "value": "application/xslt+xml"}]}, {"source": "/assets/css/(.*)", "headers": [{"key": "Content-Type", "value": "text/css"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/js/(.*)", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />

    <!-- Primary Meta Tags - Will be overridden by react-helmet-async -->
    <title>Elanswer – AI Automation & Workflow Solutions for Businesses</title>
    <meta name="description" content="Elanswer helps small & medium businesses automate workflows with AI agents, SaaS solutions, and custom automations. Save time, boost sales, and scale smarter." />
    <meta name="author" content="Elanswer" />

    <!-- Open Graph / Facebook - Will be overridden by react-helmet-async -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://elanswer.com/" />
    <meta property="og:title" content="Elanswer – AI Automation & Workflow Solutions for Businesses" />
    <meta property="og:description" content="Elanswer helps small & medium businesses automate workflows with AI agents, SaaS solutions, and custom automations. Save time, boost sales, and scale smarter." />
    <meta property="og:image" content="https://elanswer.com/og-image.png" />

    <!-- Twitter - Will be overridden by react-helmet-async -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Elanswer – AI Automation & Workflow Solutions for Businesses" />
    <meta name="twitter:description" content="Elanswer helps small & medium businesses automate workflows with AI agents, SaaS solutions, and custom automations. Save time, boost sales, and scale smarter." />
    <meta name="twitter:image" content="https://elanswer.com/og-image.png" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="shortcut icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/favicon.png" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

# =============================================================================
# NGINX SERVER CONFIGURATION FOR ELANSWER
# =============================================================================
# Server block configuration for the Elanswer React SPA with optimized
# caching, security, and single-page application routing support
# =============================================================================

server {
    listen 3000;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;
    
    # Security: Hide nginx version
    server_tokens off;
    
    # Logging
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 '{"status":"healthy","service":"elanswer-web","timestamp":"$time_iso8601"}';
        add_header Content-Type application/json;
    }
    
    # Static assets with long-term caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # CORS headers for fonts and assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
            add_header Access-Control-Max-Age 86400;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Service worker with no caching
    location /sw.js {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # Manifest and other PWA files
    location ~* \.(manifest|webmanifest)$ {
        expires 1d;
        add_header Cache-Control "public";
        add_header Content-Type application/manifest+json;
    }
    
    # SEO files
    location ~* \.(xml|txt)$ {
        expires 1d;
        add_header Cache-Control "public";
        
        # Set correct content types
        location ~* \.xml$ {
            add_header Content-Type application/xml;
        }
        location ~* \.txt$ {
            add_header Content-Type text/plain;
        }
    }
    
    # API routes (if any)
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        # Proxy to backend service (if needed)
        # proxy_pass http://backend-service;
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        
        # For now, return 404 as we don't have backend APIs
        return 404;
    }
    
    # Main application - SPA routing
    location / {
        limit_req zone=general burst=10 nodelay;
        
        try_files $uri $uri/ /index.html;
        
        # Cache HTML files for short duration
        location ~* \.html$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
        
        # Security headers for HTML content
        add_header X-Frame-Options "DENY";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
